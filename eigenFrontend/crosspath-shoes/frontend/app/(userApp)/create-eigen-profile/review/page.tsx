"use client";

import React, { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useRunningProfile } from "@/app/contexts/RunningProfileContext";
import { createOrUpdateRunningProfile } from "@/actions/running-profile";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Check,
  Footprints,
  Loader2,
  Save,
  ArrowLeft,
  Sparkles,
  User,
  Compass,
  Ruler,
} from "lucide-react";
import { CreateProfileSummary } from "@/components/create-running-profile/CreateProfileSummary";

export default function ReviewPage() {
  const router = useRouter();
  const { profileData, updateProfileData } = useRunningProfile();
  // Generate a default profile name based on user data and running goal
  const defaultProfileName = useMemo(() => {
    const userName = profileData.name || "My Profile";
    const date = new Date().toLocaleDateString();
    const goal = profileData.runningGoal || "Running";
    return `${userName} - ${goal} - ${date}`;
  }, [profileData.name, profileData.runningGoal]);

  const [profileName, setProfileName] = useState(
    profileData.name || defaultProfileName
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Check if we have all required data
  const hasAllPhotos = !!(
    profileData.footImageTopLeftUrl &&
    profileData.footImageTopRightUrl &&
    profileData.footImageMedialLeftUrl &&
    profileData.footImageMedialRightUrl
  );

  const hasRunningVideo = !!(
    profileData.runningVideoSagittalUrl && profileData.runningVideoPosteriorUrl
  );

  // Check if we have analysis data
  const hasAnalysisData = !!(
    profileData.kneeDrift !== null ||
    profileData.pelvicDrop !== null ||
    profileData.posteriorStabilityScore !== null ||
    profileData.groundContactTime !== null ||
    profileData.verticalOscillationVideo !== null
  );

  // Check if we're using fake data
  const hasFakeFootImages = !!(
    (profileData.footImageTopLeftUrl &&
      (profileData.footImageTopLeftUrl.includes("fake-foot-image") ||
        profileData.footImageTopLeftUrl.includes("fake-data-placeholder"))) ||
    (profileData.footImageTopRightUrl &&
      (profileData.footImageTopRightUrl.includes("fake-foot-image") ||
        profileData.footImageTopRightUrl.includes("fake-data-placeholder"))) ||
    (profileData.footImageMedialLeftUrl &&
      (profileData.footImageMedialLeftUrl.includes("fake-foot-image") ||
        profileData.footImageMedialLeftUrl.includes(
          "fake-data-placeholder"
        ))) ||
    (profileData.footImageMedialRightUrl &&
      (profileData.footImageMedialRightUrl.includes("fake-foot-image") ||
        profileData.footImageMedialRightUrl.includes("fake-data-placeholder")))
  );

  const hasFakeRunningVideos = !!(
    (profileData.runningVideoSagittalUrl &&
      (profileData.runningVideoSagittalUrl.includes("fake-running-video") ||
        profileData.runningVideoSagittalUrl.includes(
          "fake-data-placeholder"
        ))) ||
    (profileData.runningVideoPosteriorUrl &&
      (profileData.runningVideoPosteriorUrl.includes("fake-running-video") ||
        profileData.runningVideoPosteriorUrl.includes("fake-data-placeholder")))
  );

  // If we have fake data, consider those sections complete
  const hasCompletedFootPhotos = hasAllPhotos || hasFakeFootImages;
  const hasCompletedRunningVideos = hasRunningVideo || hasFakeRunningVideos;

  const hasAnyFakeData = hasFakeFootImages || hasFakeRunningVideos;

  // Submit the profile
  const handleSubmit = async () => {
    if (!profileName.trim() || !profileData.id) return;

    setIsSubmitting(true);
    setError(null);

    try {
      // First, update the name in the database
      const result = await createOrUpdateRunningProfile(
        profileName,
        profileData.id
      );

      if (result.error) {
        throw new Error(result.error);
      }

      // Update the profile name and mark it as completed in the context
      updateProfileData({
        name: profileName,
        isCompleted: true, // Mark the profile as completed
      });

      // Mark success and prepare redirect
      setSuccess(true);

      // Redirect to the profile view page
      const profileViewHref = `/running-profiles/${profileData.id}`;
      console.log(
        "Redirecting to:",
        profileViewHref,
        "Profile ID:",
        profileData.id
      );

      setTimeout(() => {
        // Make sure we have a valid ID before redirecting
        if (profileData.id) {
          router.push(profileViewHref); // Redirect to profile view page
        } else {
          console.error("Missing profile ID for redirect");
          router.push("/running-profiles"); // Fallback to running profiles if ID is missing
        }
      }, 1500);
    } catch (error: unknown) {
      console.error("Error submitting profile:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Failed to save your profile. Please try again."
      );
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6 pb-12">
      {/* Page Title */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl sm:text-3xl font-sans font-semibold text-foreground">
          Review Profile
        </h1>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6"
      >

        <Card className="border-border/50 overflow-hidden">
          <CardHeader>
            <CardTitle className="font-sans text-xl font-semibold text-foreground">
              Profile Summary
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              Check that all required information is complete
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {hasAnyFakeData && (
              <Alert className="bg-amber-50 dark:bg-amber-950/30 border-amber-200 dark:border-amber-800 mb-4">
                <Sparkles className="h-4 w-4 text-amber-600 dark:text-amber-500" />
                <AlertTitle className="text-amber-800 dark:text-amber-500">
                  Using AI-Generated Data
                </AlertTitle>
                <AlertDescription className="text-amber-700 dark:text-amber-400 text-sm">
                  {hasFakeFootImages && hasFakeRunningVideos
                    ? "You've chosen to use AI-generated data for both foot images and running videos."
                    : hasFakeFootImages
                    ? "You've chosen to use AI-generated data for foot images."
                    : "You've chosen to use AI-generated data for running videos."}
                  <br />
                  The analysis will be based on this synthetic data.
                </AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2 p-4 bg-secondary/30 border border-border rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-foreground">
                    Foot Photos
                  </span>
                  {hasCompletedFootPhotos ? (
                    <span className="flex items-center text-sm text-green-600 dark:text-green-400 font-medium">
                      {hasFakeFootImages ? (
                        <Sparkles className="h-4 w-4 mr-1 text-amber-500" />
                      ) : (
                        <Check className="h-4 w-4 mr-1" />
                      )}
                      Complete
                    </span>
                  ) : (
                    <span className="text-sm text-yellow-600 dark:text-yellow-400 font-medium">
                      Incomplete
                    </span>
                  )}
                </div>
                <div className="text-xs text-muted-foreground leading-snug">
                  {hasCompletedFootPhotos
                    ? hasFakeFootImages
                      ? "Using AI-generated foot images."
                      : "All required foot photos have been uploaded."
                    : "Some foot photos are missing. Go back to complete them."}
                </div>
              </div>

              <div className="space-y-2 p-4 bg-secondary/30 border border-border rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-foreground">
                    Running Videos
                  </span>
                  {hasCompletedRunningVideos ? (
                    <span className="flex items-center text-sm text-green-600 dark:text-green-400 font-medium">
                      {hasFakeRunningVideos ? (
                        <Sparkles className="h-4 w-4 mr-1 text-amber-500" />
                      ) : (
                        <Check className="h-4 w-4 mr-1" />
                      )}
                      Complete
                    </span>
                  ) : (
                    <span className="text-sm text-yellow-600 dark:text-yellow-400 font-medium">
                      Incomplete
                    </span>
                  )}
                </div>
                <div className="text-xs text-muted-foreground leading-snug">
                  {hasCompletedRunningVideos
                    ? hasFakeRunningVideos
                      ? "Using AI-generated running videos."
                      : "Running videos have been uploaded."
                    : "Running videos are missing. Go back to upload them."}
                </div>
              </div>

              <div className="space-y-2 p-4 bg-secondary/30 border border-border rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-foreground">
                    Runner Info
                  </span>
                  {profileData.age && profileData.heightCm ? (
                    <span className="flex items-center text-sm text-green-600 dark:text-green-400 font-medium">
                      <Check className="h-4 w-4 mr-1" /> Complete
                    </span>
                  ) : (
                    <span className="text-sm text-yellow-600 dark:text-yellow-400 font-medium">
                      Incomplete
                    </span>
                  )}
                </div>
                <div className="text-xs text-muted-foreground leading-snug">
                  {profileData.age && profileData.heightCm
                    ? "Runner details provided."
                    : "Runner details incomplete. Go back to complete them."}
                </div>
              </div>

              <div className="space-y-2 p-4 bg-secondary/30 border border-border rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-foreground">
                    Analysis Data
                  </span>
                  {hasAnalysisData ? (
                    <span className="flex items-center text-sm text-green-600 dark:text-green-400 font-medium">
                      {hasAnyFakeData ? (
                        <Sparkles className="h-4 w-4 mr-1 text-amber-500" />
                      ) : (
                        <Check className="h-4 w-4 mr-1" />
                      )}
                      Ready
                    </span>
                  ) : (
                    <span className="text-sm text-yellow-600 dark:text-yellow-400 font-medium">
                      Pending
                    </span>
                  )}
                </div>
                <div className="text-xs text-muted-foreground leading-snug">
                  {hasAnalysisData
                    ? hasAnyFakeData
                      ? "AI-generated analysis data is ready."
                      : "Analysis data is ready."
                    : "Analysis will be performed after saving."}
                </div>
              </div>
            </div>

            {/* Add CreateProfileSummary component */}
            <div className="mt-6">
              <CreateProfileSummary profileData={profileData} />
            </div>
          </CardContent>
        </Card>

        <Card className="border-border/50 overflow-hidden">
          <CardHeader>
            <CardTitle className="flex items-center font-sans text-xl font-semibold text-foreground">
              <Footprints className="h-5 w-5 mr-2 text-primary" />
              Profile Details
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              Name your profile and review your key data points.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-3 bg-secondary/30 p-4 rounded-lg border border-border">
              <Label
                htmlFor="profileName"
                className="text-sm font-medium text-foreground"
              >
                Profile Name
              </Label>
              <Input
                id="profileName"
                value={profileName}
                onChange={(e) => setProfileName(e.target.value)}
                placeholder="Enter a name for your profile"
                className="max-w-md border-border focus:border-primary text-base font-medium"
              />
              <p className="text-xs text-muted-foreground">
                This name will help you identify this profile in your dashboard.
              </p>
            </div>

            {/* Runner Information */}
            <div className="space-y-3">
              <h3 className="text-base font-medium text-primary flex items-center gap-2">
                <User className="h-4 w-4" /> Runner Information
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {profileData.age && (
                  <div className="bg-secondary/30 p-3 rounded-lg border border-border">
                    <span className="text-xs text-muted-foreground uppercase tracking-wider block mb-1">
                      Age
                    </span>
                    <span className="text-sm font-medium text-foreground">
                      {profileData.age} years
                    </span>
                  </div>
                )}
                {profileData.heightCm && (
                  <div className="bg-secondary/30 p-3 rounded-lg border border-border">
                    <span className="text-xs text-muted-foreground uppercase tracking-wider block mb-1">
                      Height
                    </span>
                    <span className="text-sm font-medium text-foreground">
                      {profileData.heightCm} cm
                    </span>
                  </div>
                )}
                {profileData.weightKg && (
                  <div className="bg-secondary/30 p-3 rounded-lg border border-border">
                    <span className="text-xs text-muted-foreground uppercase tracking-wider block mb-1">
                      Weight
                    </span>
                    <span className="text-sm font-medium text-foreground">
                      {profileData.weightKg} kg
                    </span>
                  </div>
                )}
                {profileData.gender && (
                  <div className="bg-secondary/30 p-3 rounded-lg border border-border">
                    <span className="text-xs text-muted-foreground uppercase tracking-wider block mb-1">
                      Gender
                    </span>
                    <span className="text-sm font-medium text-foreground">
                      {profileData.gender}
                    </span>
                  </div>
                )}
                {profileData.runningGoal && (
                  <div className="bg-secondary/30 p-3 rounded-lg border border-border">
                    <span className="text-xs text-muted-foreground uppercase tracking-wider block mb-1">
                      Running Goal
                    </span>
                    <span className="text-sm font-medium text-foreground">
                      {profileData.runningGoal}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Running Environment */}
            <div className="space-y-3">
              <h3 className="text-base font-medium text-primary flex items-center gap-2">
                <Compass className="h-4 w-4" /> Running Environment
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {profileData.terrain && (
                  <div className="bg-secondary/30 p-3 rounded-lg border border-border">
                    <span className="text-xs text-muted-foreground uppercase tracking-wider block mb-1">
                      Terrain
                    </span>
                    <span className="text-sm font-medium text-foreground">
                      {profileData.terrain}
                    </span>
                  </div>
                )}
                {profileData.climate && (
                  <div className="bg-secondary/30 p-3 rounded-lg border border-border">
                    <span className="text-xs text-muted-foreground uppercase tracking-wider block mb-1">
                      Climate
                    </span>
                    <span className="text-sm font-medium text-foreground">
                      {profileData.climate}
                    </span>
                  </div>
                )}
                {profileData.averageWeeklyKm && (
                  <div className="bg-secondary/30 p-3 rounded-lg border border-border">
                    <span className="text-xs text-muted-foreground uppercase tracking-wider block mb-1">
                      Weekly Distance
                    </span>
                    <span className="text-sm font-medium text-foreground">
                      {profileData.averageWeeklyKm} km
                    </span>
                  </div>
                )}
                {profileData.averagePaceEasyLong && (
                  <div className="bg-secondary/30 p-3 rounded-lg border border-border">
                    <span className="text-xs text-muted-foreground uppercase tracking-wider block mb-1">
                      Average Pace
                    </span>
                    <span className="text-sm font-medium text-foreground">
                      {profileData.averagePaceEasyLong}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Foot Measurements */}
            {(profileData.heelToToeLengthLeft ||
              profileData.heelToToeLengthRight) && (
              <div className="space-y-3">
                <h3 className="text-base font-medium text-primary flex items-center gap-2">
                  <Ruler className="h-4 w-4" /> Foot Measurements
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <h4 className="text-sm font-medium mb-2 text-foreground">Left Foot</h4>
                    <div className="space-y-2">
                      {profileData.heelToToeLengthLeft && (
                        <div className="bg-secondary/30 p-3 rounded-lg border border-border">
                          <span className="text-xs text-muted-foreground uppercase tracking-wider block mb-1">
                            Length
                          </span>
                          <span className="text-sm font-medium text-foreground">
                            {profileData.heelToToeLengthLeft} mm
                          </span>
                        </div>
                      )}
                      {profileData.forefootWidthLeft && (
                        <div className="bg-secondary/30 p-3 rounded-lg border border-border">
                          <span className="text-xs text-muted-foreground uppercase tracking-wider block mb-1">
                            Width
                          </span>
                          <span className="text-sm font-medium text-foreground">
                            {profileData.forefootWidthLeft} mm
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium mb-2 text-foreground">Right Foot</h4>
                    <div className="space-y-2">
                      {profileData.heelToToeLengthRight && (
                        <div className="bg-secondary/30 p-3 rounded-lg border border-border">
                          <span className="text-xs text-muted-foreground uppercase tracking-wider block mb-1">
                            Length
                          </span>
                          <span className="text-sm font-medium text-foreground">
                            {profileData.heelToToeLengthRight} mm
                          </span>
                        </div>
                      )}
                      {profileData.forefootWidthRight && (
                        <div className="bg-secondary/30 p-3 rounded-lg border border-border">
                          <span className="text-xs text-muted-foreground uppercase tracking-wider block mb-1">
                            Width
                          </span>
                          <span className="text-sm font-medium text-foreground">
                            {profileData.forefootWidthRight} mm
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Analysis Data */}
            {hasAnalysisData && (
              <div className="space-y-3">
                <h3 className="text-lg font-medium text-brand-green dark:text-emerald-400 flex items-center gap-2">
                  <Sparkles className="h-5 w-5" /> Analysis Results
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {profileData.posteriorStabilityScore !== null && (
                    <div className="bg-zinc-100/60 dark:bg-sweetspot-black/60 p-3 rounded-sm border border-brand-black/10 dark:border-text-main/10">
                      <span className="text-xs font-input text-brand-black/60 dark:text-text-main/60 uppercase tracking-wider block mb-0.5">
                        Stability Score
                      </span>
                      <span className="text-base font-medium text-brand-black/90 dark:text-text-main/90">
                        {profileData.posteriorStabilityScore?.toFixed(0) || 0}
                        /100
                      </span>
                    </div>
                  )}
                  {profileData.pelvicDrop !== null && (
                    <div className="bg-zinc-100/60 dark:bg-sweetspot-black/60 p-3 rounded-sm border border-brand-black/10 dark:border-text-main/10">
                      <span className="text-xs font-input text-brand-black/60 dark:text-text-main/60 uppercase tracking-wider block mb-0.5">
                        Pelvic Drop
                      </span>
                      <span className="text-base font-medium text-brand-black/90 dark:text-text-main/90">
                        {profileData.pelvicDrop?.toFixed(1) || 0}°
                      </span>
                    </div>
                  )}
                  {profileData.kneeDrift !== null && (
                    <div className="bg-zinc-100/60 dark:bg-sweetspot-black/60 p-3 rounded-sm border border-brand-black/10 dark:border-text-main/10">
                      <span className="text-xs font-input text-brand-black/60 dark:text-text-main/60 uppercase tracking-wider block mb-0.5">
                        Knee Drift
                      </span>
                      <span className="text-base font-medium text-brand-black/90 dark:text-text-main/90">
                        {profileData.kneeDrift?.toFixed(1) || 0} cm
                      </span>
                    </div>
                  )}
                  {profileData.groundContactTime !== null && (
                    <div className="bg-zinc-100/60 dark:bg-sweetspot-black/60 p-3 rounded-sm border border-brand-black/10 dark:border-text-main/10">
                      <span className="text-xs font-input text-brand-black/60 dark:text-text-main/60 uppercase tracking-wider block mb-0.5">
                        Ground Contact Time
                      </span>
                      <span className="text-base font-medium text-brand-black/90 dark:text-text-main/90">
                        {profileData.groundContactTime} ms
                      </span>
                    </div>
                  )}
                  {profileData.verticalOscillationVideo !== null && (
                    <div className="bg-zinc-100/60 dark:bg-sweetspot-black/60 p-3 rounded-sm border border-brand-black/10 dark:border-text-main/10">
                      <span className="text-xs font-input text-brand-black/60 dark:text-text-main/60 uppercase tracking-wider block mb-0.5">
                        Vertical Oscillation
                      </span>
                      <span className="text-base font-medium text-brand-black/90 dark:text-text-main/90">
                        {profileData.verticalOscillationVideo?.toFixed(1) || 0}{" "}
                        cm
                      </span>
                    </div>
                  )}
                  {profileData.overstride !== null && (
                    <div className="bg-zinc-100/60 dark:bg-sweetspot-black/60 p-3 rounded-sm border border-brand-black/10 dark:border-text-main/10">
                      <span className="text-xs font-input text-brand-black/60 dark:text-text-main/60 uppercase tracking-wider block mb-0.5">
                        Overstride
                      </span>
                      <span className="text-base font-medium text-brand-black/90 dark:text-text-main/90">
                        {profileData.overstride?.toFixed(1) || 0} cm
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-center pt-6">
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || !profileName.trim() || success}
              className="w-full md:w-auto bg-primary text-primary-foreground hover:bg-primary/90"
              size="lg"
            >
              {isSubmitting ? (
                <span className="flex items-center justify-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Saving Profile...
                </span>
              ) : success ? (
                <span className="flex items-center justify-center gap-2">
                  <Check className="h-4 w-4" />
                  Profile Saved!
                </span>
              ) : (
                <span className="flex items-center justify-center gap-2">
                  <Save className="h-4 w-4" />
                  Save & View Profile
                </span>
              )}
            </Button>
          </CardFooter>
        </Card>

        {error && (
          <Alert
            variant="destructive"
            className="bg-red-100 dark:bg-red-900/30 border-red-500/50 text-red-700 dark:text-red-400"
          >
            <AlertTitle className="font-sans font-medium">Error</AlertTitle>
            <AlertDescription className="font-input">{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="bg-emerald-50 dark:bg-brand-green/10 border-emerald-700/30 dark:border-brand-green/30 text-emerald-700 dark:text-brand-green">
            <Check className="h-4 w-4 text-emerald-700 dark:text-brand-green" />
            <AlertTitle className="font-sans font-medium">Success!</AlertTitle>
            <AlertDescription className="font-input text-emerald-700/80 dark:text-text-main/80">
              Your running profile has been saved. Redirecting to your profile
              overview...
            </AlertDescription>
          </Alert>
        )}

        {!success && (
          <div className="mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={isSubmitting}
              className="border-border hover:bg-secondary"
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </Button>
          </div>
        )}
      </motion.div>
    </div>
  );
}
