"use client"

import { motion } from "framer-motion"
import { useRouter, usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { useRunningProfile } from "@/app/contexts/RunningProfileContext"
import { CircularProgress } from "./CircularProgress"


export function CreateProfileStepper() {
  const router = useRouter()
  const pathname = usePathname()
  const { steps, currentStep } = useRunningProfile()

  // Don't show stepper on the start page
  if (pathname === "/create-eigen-profile") {
    return null
  }

  // Don't show stepper on upload pages since UnifiedUploadStep has its own compact stepper
  if (pathname.includes("/foot-imaging/") || pathname.includes("/running-videos/")) {
    return null
  }

  const currentStepInfo = steps[currentStep]
  const nextStepInfo = steps[currentStep + 1]

  return (
    <div className="w-full">
      {/* Progress Circle and Step Info */}
      <div className="flex flex-col items-center mb-8">
        <CircularProgress
          currentStep={currentStep + 1}
          totalSteps={steps.length}
          size={80}
          className="mb-3"
        />
        <div className="text-center">
          <h2 className="text-lg font-semibold text-foreground">
            {currentStepInfo?.title || "Step"}
          </h2>
        </div>
      </div>
    </div>
  )
}
