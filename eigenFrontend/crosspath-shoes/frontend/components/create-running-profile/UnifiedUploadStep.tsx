"use client";

import React, { useState, useEffect, useTransition } from "react";
import Image from "next/image";
import { useRunningProfile } from "@/app/contexts/RunningProfileContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import {
  UploadCloud,
  Trash2,
  Loader2,
  Video,
  ArrowLeft,
  ArrowRight,
  Camera,
  Play,
} from "lucide-react";
import { uploadFile } from "@/lib/upload-helpers";
import { toast } from "sonner";
import { motion } from "framer-motion";

type MediaType = "image" | "video";
type FieldName =
  | "footImageTopLeftUrl"
  | "footImageTopRightUrl"
  | "footImageMedialLeftUrl"
  | "footImageMedialRightUrl"
  | "runningVideoPosteriorUrl"
  | "runningVideoSagittalUrl";

interface UnifiedUploadStepProps {
  title: string;
  fieldName: FieldName;
  mediaType: MediaType;
  nextStepHref: string;
  backStepHref: string;
  currentStepIndex: number;
}

// Content guidance for each upload type
const getGuidanceContent = (fieldName: FieldName, mediaType: MediaType) => {
  if (mediaType === "image") {
    switch (fieldName) {
      case "footImageTopLeftUrl":
        return "Place your left foot on a white sheet of paper and take a photo from directly above. Ensure your entire foot is visible and the image is well-lit.";
      case "footImageTopRightUrl":
        return "Place your right foot on a white sheet of paper and take a photo from directly above. Ensure your entire foot is visible and the image is well-lit.";
      case "footImageMedialLeftUrl":
        return "Stand with your left foot's inner side facing the camera. Capture the arch and ankle area clearly from about 2 feet away.";
      case "footImageMedialRightUrl":
        return "Stand with your right foot's inner side facing the camera. Capture the arch and ankle area clearly from about 2 feet away.";
      default:
        return "Take a clear, well-lit photo following the specific positioning instructions.";
    }
  } else {
    switch (fieldName) {
      case "runningVideoSagittalUrl":
        return "Record 10-15 seconds of yourself running at a comfortable pace from the side view. Ensure your full body is visible and the camera is steady.";
      case "runningVideoPosteriorUrl":
        return "Record 10-15 seconds of yourself running at a comfortable pace from behind. Focus on capturing your leg movement and foot strike pattern.";
      default:
        return "Record a short video following the specific angle and duration instructions.";
    }
  }
};

// Compact stepper component for top-left corner
const CompactStepper: React.FC<{
  currentStep: number;
  totalSteps: number;
  currentStepTitle: string;
  nextStepTitle?: string;
}> = ({ currentStep, totalSteps, currentStepTitle, nextStepTitle }) => {
  const radius = 20;
  const circumference = radius * 2 * Math.PI;
  const progress = (currentStep / totalSteps) * 100;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <div className="flex items-start gap-3">
      {/* Circular Progress */}
      <div className="relative flex-shrink-0">
        <svg
          width={48}
          height={48}
          className="transform -rotate-90"
          viewBox="0 0 48 48"
        >
          {/* Background circle */}
          <circle
            cx={24}
            cy={24}
            r={radius}
            stroke="currentColor"
            strokeWidth={3}
            fill="transparent"
            className="text-muted/30"
          />
          {/* Progress circle */}
          <motion.circle
            cx={24}
            cy={24}
            r={radius}
            stroke="currentColor"
            strokeWidth={3}
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="text-primary transition-all duration-300 ease-in-out"
            initial={{ strokeDashoffset: circumference }}
            animate={{ strokeDashoffset }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
          />
        </svg>
        {/* Step counter in center */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="text-sm font-semibold text-foreground">
              {currentStep}
            </div>
            <div className="text-[10px] text-muted-foreground">
              of {totalSteps}
            </div>
          </div>
        </div>
      </div>

      {/* Step Info */}
      <div className="flex flex-col justify-center min-h-[48px]">
        <h2 className="text-base font-semibold text-foreground leading-tight">
          {currentStepTitle}
        </h2>
        {nextStepTitle && (
          <p className="text-xs text-muted-foreground mt-0.5">
            Next: {nextStepTitle}
          </p>
        )}
      </div>
    </div>
  );
};

export const UnifiedUploadStep: React.FC<UnifiedUploadStepProps> = ({
  title,
  fieldName,
  mediaType,
  nextStepHref,
  backStepHref,
  currentStepIndex,
}) => {
  const { profileId, profileData, updateProfileData, setCurrentStep, steps } = useRunningProfile();
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Get next step title for stepper
  const nextStepTitle = steps[currentStepIndex + 1]?.title;

  useEffect(() => {
    const existingUrl = profileData[fieldName] as string | null;
    if (existingUrl) {
      setPreviewUrl(existingUrl);
    }
  }, [profileData, fieldName]);

  useEffect(() => {
    if (file) {
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      return () => URL.revokeObjectURL(objectUrl);
    } else if (!profileData[fieldName]) {
      setPreviewUrl(null);
    }
  }, [file, profileData, fieldName]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];

      // Basic validation
      if (mediaType === "image" && !selectedFile.type.startsWith("image/")) {
        setError("Please select an image file.");
        return;
      }
      if (mediaType === "video" && !selectedFile.type.startsWith("video/")) {
        setError("Please select a video file.");
        return;
      }

      setFile(selectedFile);
      setError(null);
    }
  };

  const handleRemove = () => {
    setFile(null);
    setError(null);
    setPreviewUrl(profileData[fieldName] as string | null);
  };

  const handleNext = () => {
    if (!file) {
      setError(`Please select ${mediaType === "image" ? "an image" : "a video"} to continue.`);
      return;
    }
    if (!profileId) {
      setError("Profile not found. Please try again.");
      return;
    }

    setError(null);
    setIsUploading(true);

    startTransition(async () => {
      try {
        const result = await uploadFile(file, profileId, fieldName);

        if (result.error) {
          throw new Error(result.error);
        }

        if (result.fileUrl) {
          updateProfileData({ [fieldName]: result.fileUrl });
          toast.success(`${mediaType === "image" ? "Image" : "Video"} uploaded successfully!`);
          setCurrentStep(currentStepIndex + 1);
          router.push(nextStepHref);
        }
      } catch (err) {
        const message = err instanceof Error ? err.message : "Upload failed";
        setError(message);
        toast.error(message);
      } finally {
        setIsUploading(false);
      }
    });
  };

  const handleBack = () => {
    setCurrentStep(currentStepIndex - 1);
    router.push(backStepHref);
  };

  const isLoading = isUploading || isPending;
  const accept = mediaType === "image" ? "image/*" : "video/*";
  const fileId = `${mediaType}Upload`;
  const guidanceText = getGuidanceContent(fieldName, mediaType);

  return (
    <div className="h-[calc(100vh-120px)] flex flex-col max-w-5xl mx-auto px-4">
      {/* Compact Stepper - Top Left */}
      <div className="mb-4">
        <CompactStepper
          currentStep={currentStepIndex + 1}
          totalSteps={steps.length}
          currentStepTitle={title}
          nextStepTitle={nextStepTitle}
        />
      </div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex-1 flex flex-col min-h-0"
      >
        {/* Guidance Text */}
        <div className="text-center mb-4">
          <p className="text-sm text-muted-foreground max-w-2xl mx-auto">
            {guidanceText}
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="p-2 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 border border-red-500/50 text-sm rounded-lg text-center mb-3">
            {error}
          </div>
        )}

        {/* Content Area - Two Column Layout on Desktop */}
        <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-4 min-h-0">
          {/* Upload Controls */}
          <div className="flex flex-col justify-center space-y-3">
            <Input
              id={fileId}
              type="file"
              accept={accept}
              onChange={handleFileChange}
              className="hidden"
              disabled={isLoading}
            />

            <Label
              htmlFor={fileId}
              className={`flex items-center justify-center gap-2 cursor-pointer rounded-lg border-2 border-dashed border-muted-foreground/25 bg-muted/30 px-3 py-4 text-sm font-medium text-muted-foreground transition-colors hover:bg-muted/50 hover:border-muted-foreground/50 ${
                isLoading ? "opacity-50 cursor-not-allowed" : ""
              }`}
            >
              {mediaType === "image" ? (
                <Camera className="h-4 w-4" />
              ) : (
                <Play className="h-4 w-4" />
              )}
              <span className="text-sm">
                {file ? `Change ${mediaType}` : `Select ${mediaType}`}
              </span>
            </Label>

            {(file || profileData[fieldName]) && (
              <Button
                variant="outline"
                onClick={handleRemove}
                disabled={isLoading}
                className="flex items-center justify-center gap-2 text-destructive hover:text-destructive border-destructive/50 hover:border-destructive hover:bg-destructive/5"
                size="sm"
              >
                <Trash2 className="h-4 w-4" />
                Remove
              </Button>
            )}
          </div>

          {/* Preview Area */}
          <div className="border border-border rounded-lg p-2 bg-muted/20 flex items-center justify-center min-h-[180px] lg:min-h-[240px]">
            {previewUrl ? (
              <div className="relative w-full h-full max-h-[160px] lg:max-h-[220px]">
                {mediaType === "image" ? (
                  <Image
                    src={previewUrl}
                    alt="Preview"
                    fill
                    className="object-contain rounded-lg"
                    priority
                  />
                ) : (
                  <video
                    src={previewUrl}
                    controls
                    className="w-full h-full max-h-[160px] lg:max-h-[220px] object-contain rounded-lg"
                  >
                    Your browser does not support the video tag.
                  </video>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center text-center">
                {mediaType === "image" ? (
                  <Camera className="h-10 w-10 text-muted-foreground/40 mb-2" />
                ) : (
                  <Video className="h-10 w-10 text-muted-foreground/40 mb-2" />
                )}
                <p className="text-xs text-muted-foreground">
                  Preview will appear here
                </p>
              </div>
            )}
          </div>
        </div>
      </motion.div>

      {/* Navigation Buttons - Wider and closer together */}
      <div className="flex items-center justify-center pt-4 gap-3 mt-auto">
        <Button
          variant="outline"
          onClick={handleBack}
          disabled={isLoading}
          className="flex items-center gap-2 flex-1 max-w-[200px]"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>

        <Button
          onClick={handleNext}
          disabled={!file || isLoading}
          className="bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-2 flex-1 max-w-[200px]"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              Next
              <ArrowRight className="h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
};
